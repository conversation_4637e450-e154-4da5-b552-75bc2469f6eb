<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>测试layuimini-content-href链接</title>
    <link rel="stylesheet" href="lib/layui-v2.9.21/css/layui.css" media="all">
    <script src="lib/layui-v2.9.21/layui.js" charset="utf-8"></script>
    <script src="js/navigate.js" charset="utf-8"></script>
</head>
<body>
    <div class="layui-container" style="margin-top: 20px;">
        <h2>测试layuimini-content-href链接</h2>
        
        <div class="layui-card">
            <div class="layui-card-header">测试链接</div>
            <div class="layui-card-body">
                <p>点击下面的按钮测试不同的跳转方式：</p>
                
                <!-- 使用layuimini-content-href属性 -->
                <a class="layui-btn layui-btn-normal" layuimini-content-href="../Flow/CustomerTrans?RFQNo=TEST001" data-title="测试页面 - TEST001">
                    测试layuimini-content-href
                </a>
                
                <!-- 使用普通href -->
                <a class="layui-btn layui-btn-primary" href="../Flow/CustomerTrans?RFQNo=TEST001">
                    测试普通href
                </a>
                
                <!-- 使用JavaScript调用 -->
                <button class="layui-btn layui-btn-warm" onclick="testNavigateFunction()">
                    测试navigateInNewTab函数
                </button>
                
                <!-- 调试按钮 -->
                <button class="layui-btn layui-btn-danger" onclick="debugEnvironment()">
                    调试环境信息
                </button>
            </div>
        </div>
        
        <div class="layui-card">
            <div class="layui-card-header">调试信息</div>
            <div class="layui-card-body">
                <div id="debugInfo" style="background: #f2f2f2; padding: 10px; font-family: monospace; white-space: pre-wrap;"></div>
            </div>
        </div>
    </div>

    <script>
        layui.use(['layer'], function(){
            var layer = layui.layer;
            
            // 重写console.log来显示在页面上
            var originalLog = console.log;
            var originalError = console.error;
            
            function logToPage(message, type) {
                originalLog.apply(console, arguments);
                var debugInfo = document.getElementById('debugInfo');
                var timestamp = new Date().toLocaleTimeString();
                var prefix = type === 'error' ? '[ERROR]' : '[LOG]';
                debugInfo.innerHTML += timestamp + ' ' + prefix + ' ' + message + '\n';
                debugInfo.scrollTop = debugInfo.scrollHeight;
            }
            
            console.log = function() {
                var message = Array.prototype.slice.call(arguments).join(' ');
                logToPage(message, 'log');
            };
            
            console.error = function() {
                var message = Array.prototype.slice.call(arguments).join(' ');
                logToPage(message, 'error');
            };
            
            // 添加自定义的layuimini-content-href事件监听器
            $(document).on('click', '[layuimini-content-href]', function(e) {
                e.preventDefault();
                var href = $(this).attr('layuimini-content-href');
                var title = $(this).attr('data-title') || '新标签页';
                
                console.log('点击了layuimini-content-href链接:', href, title);
                
                // 检查是否在iframe环境中
                if (window.parent && window.parent !== window) {
                    console.log('在iframe环境中，使用navigateInNewTab');
                    // 在iframe中，使用我们的navigateInNewTab函数
                    if (typeof navigateInNewTab === 'function') {
                        navigateInNewTab(href, title);
                    } else {
                        console.error('navigateInNewTab函数未找到');
                        layer.msg('navigateInNewTab函数未找到', {icon: 2});
                    }
                } else {
                    console.log('不在iframe环境中，模拟跳转');
                    layer.msg('模拟跳转到: ' + href, {icon: 1});
                }
            });
        });
        
        function testNavigateFunction() {
            console.log('测试navigateInNewTab函数...');
            if (typeof navigateInNewTab === 'function') {
                navigateInNewTab('../Flow/CustomerTrans?RFQNo=TEST001', '测试页面 - TEST001');
            } else {
                console.error('navigateInNewTab函数未找到');
                layui.layer.msg('navigateInNewTab函数未找到', {icon: 2});
            }
        }
        
        function debugEnvironment() {
            console.log('=== 环境调试信息 ===');
            console.log('window.parent === window:', window.parent === window);
            console.log('typeof navigateInNewTab:', typeof navigateInNewTab);
            console.log('typeof window.parent.layui:', typeof window.parent.layui);
            if (window.parent.layui) {
                console.log('typeof window.parent.layui.miniTab:', typeof window.parent.layui.miniTab);
                if (window.parent.layui.miniTab) {
                    console.log('typeof window.parent.layui.miniTab.openNewTabByIframe:', typeof window.parent.layui.miniTab.openNewTabByIframe);
                }
            }
            console.log('当前页面URL:', window.location.href);
            console.log('父页面URL:', window.parent.location.href);
            console.log('=== 调试信息结束 ===');
        }
    </script>
</body>
</html>
