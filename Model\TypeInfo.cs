﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RFQ.Dao.Model
{
    public partial class TypeInfo : Entity
    {
        public TypeInfo()
        {
            this.RFQ_NO = string.Empty;
            this.MODEL = string.Empty;
            this.MODEL_DESC = string.Empty;
            this.BOMQTYFIELD = string.Empty;
            this.CREATE_TIME = DateTime.Now;
        }

        /// <summary>
        /// RFQ流程编号
        /// </summary>
        public string RFQ_NO { get; set; }

        /// <summary>
        /// 型号
        /// </summary>
        public string MODEL { get; set; }

        /// <summary>
        /// 型号描述
        /// </summary>
        public string MODEL_DESC { get; set; }

        /// <summary>
        /// EAU1
        /// </summary>
        public int? EAU1 { get; set; }

        /// <summary>
        /// EAU2
        /// </summary>
        public int? EAU2 { get; set; }

        /// <summary>
        /// EAU3
        /// </summary>
        public int? EAU3 { get; set; }

        /// <summary>
        /// BOM数量字段
        /// </summary>
        public string BOMQTYFIELD { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CREATE_TIME { get; set; }
    }
}
