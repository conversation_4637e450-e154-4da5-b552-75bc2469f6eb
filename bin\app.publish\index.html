<html>
<head>
    <meta charset="utf-8">
    <title>layuimini-iframe版 v2 - 基于Layui的后台管理系统前端模板</title>
    <meta name="keywords" content="layuimini,layui,layui模板,layui后台,后台模板,admin,admin模板,layui mini">
    <meta name="description" content="layuimini基于layui的轻量级前端后台管理框架，最简洁、易用的后台框架模板，面向所有层次的前后端程序,只需提供一个接口就直接初始化整个框架，无需复杂操作。">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta http-equiv="Access-Control-Allow-Origin" content="*">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="icon" href="images/favicon.ico">
    <link rel="stylesheet" href="./lib/layui-v2.9.21/css/layui.css" media="all">
    <link rel="stylesheet" href="./css/layuimini.css?v=*******" media="all">
    <link rel="stylesheet" href="./css/themes/default.css" media="all">
    <link rel="stylesheet" href="./lib/font-awesome-4.7.0/css/font-awesome.min.css" media="all">
    <!--[if lt IE 9]>
    <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
    <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style id="layuimini-bg-color">
    </style>
</head>
<body class="layui-layout-body layuimini-all">
<div class="layui-layout layui-layout-admin">

    <div class="layui-header header">
        <div class="layui-logo layuimini-logo"></div>

        <div class="layuimini-header-content">
            <a>
                <div class="layuimini-tool"><i title="展开" class="fa fa-outdent" data-side-fold="1"></i></div>
            </a>

            <!--电脑端头部菜单-->
            <ul class="layui-nav layui-layout-left layuimini-header-menu layuimini-menu-header-pc layuimini-pc-show">
            </ul>

            <!--手机端头部菜单-->
            <ul class="layui-nav layui-layout-left layuimini-header-menu layuimini-mobile-show">
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="fa fa-list-ul"></i> 选择模块</a>
                    <dl class="layui-nav-child layuimini-menu-header-mobile">
                    </dl>
                </li>
            </ul>

            <ul class="layui-nav layui-layout-right">

                <li class="layui-nav-item" lay-unselect>
                    <a href="javascript:;" data-refresh="刷新"><i class="fa fa-refresh"></i></a>
                </li>
                <li class="layui-nav-item" lay-unselect>
                    <a href="javascript:;" data-clear="清理" class="layuimini-clear"><i class="fa fa-trash-o"></i></a>
                </li>
                <li class="layui-nav-item mobile layui-hide-xs" lay-unselect>
                    <a href="javascript:;" data-check-screen="full"><i class="fa fa-arrows-alt"></i></a>
                </li>
                <li class="layui-nav-item layuimini-setting">
                    <a href="javascript:;">admin</a>
                    <dl class="layui-nav-child">
                        <dd>
                            <a href="javascript:;" layuimini-content-href="page/user-setting.html" data-title="基本资料" data-icon="fa fa-gears">基本资料<span class="layui-badge-dot"></span></a>
                        </dd>
                        <dd>
                            <a href="javascript:;" layuimini-content-href="SystemManager/ChangePassword.aspx" data-title="修改密码" data-icon="fa fa-gears">修改密码</a>
                        </dd>
                        <dd>
                            <hr>
                        </dd>
                        <dd>
                            <a href="javascript:;" class="login-out">退出登录</a>
                        </dd>
                    </dl>
                </li>
                <li class="layui-nav-item layuimini-select-bgcolor" lay-unselect>
                    <a href="javascript:;" data-bgcolor="配色方案"><i class="fa fa-dashboard"></i></a>
                </li>
            </ul>
        </div>
    </div>

    <!--无限极左侧菜单-->
    <div class="layui-side layui-bg-black layuimini-menu-left">
    </div>

    <!--初始化加载层-->
    <div class="layuimini-loader">
        <div class="layuimini-loader-inner"></div>
    </div>

    <!--手机端遮罩层-->
    <div class="layuimini-make"></div>

    <!-- 移动导航 -->
    <div class="layuimini-site-mobile"><i class="layui-icon"></i></div>

    <div class="layui-body">

        <div class="layuimini-tab layui-tab-rollTool layui-tab" lay-filter="layuiminiTab" lay-allowclose="true">
            <ul class="layui-tab-title">
                <li class="layui-this" id="layuiminiHomeTabId" lay-id=""></li>
            </ul>
            <div class="layui-tab-control">
                <li class="layuimini-tab-roll-left layui-icon layui-icon-left"></li>
                <li class="layuimini-tab-roll-right layui-icon layui-icon-right"></li>
                <li class="layui-tab-tool layui-icon layui-icon-down">
                    <ul class="layui-nav close-box">
                        <li class="layui-nav-item">
                            <a href="javascript:;"><span class="layui-nav-more"></span></a>
                            <dl class="layui-nav-child">
                                <dd><a href="javascript:;" layuimini-tab-close="current">关 闭 当 前</a></dd>
                                <dd><a href="javascript:;" layuimini-tab-close="other">关 闭 其 他</a></dd>
                                <dd><a href="javascript:;" layuimini-tab-close="all">关 闭 全 部</a></dd>
                            </dl>
                        </li>
                    </ul>
                </li>
            </div>
            <div class="layui-tab-content">
                <div id="layuiminiHomeTabIframe" class="layui-tab-item layui-show"></div>
            </div>
        </div>
    </div>
</div>
<script src="./lib/layui-v2.9.21/layui.js" charset="utf-8"></script>
<script src="./js/lay-config.js?v=2.0.0" charset="utf-8"></script>
<script>
    layui.use(['jquery', 'layer', 'miniAdmin','miniTongji'], function () {
        var $ = layui.jquery,
            layer = layui.layer,
            miniAdmin = layui.miniAdmin;
        var a = document.cookie.match(/UserName=(\w+)/)[1];

        var options = {
            iniUrl: "ashx/HomeController.ashx",    // 初始化接口
            clearUrl: "api/clear.json", // 缓存清理接口
            urlHashLocation: true,      // 是否打开hash定位
            bgColorDefault: 0,          // 主题默认配置【主题种类型共12种，填写数字：0-11】
            multiModule: true,          // 是否开启多模块
            menuChildOpen: false,       // 是否默认展开菜单
            leftMenuIsHide: false,      // 左侧折叠菜单是否隐藏，默认是展开
            loadingTime: 0,             // 初始化加载时间
            pageAnim: true,             // iframe窗口动画
            maxTabNum: 20,              // 最大的tab打开数量
            clickHomeTabRefresh:false   // 是否每次点击tab刷新，默认是false关闭
        };
        miniAdmin.render(options);

        $('.login-out').on("click", function () {
            layer.msg('退出登录成功', function () {
                window.location = 'Login.html';
            });
        });

        // 处理hash路由刷新问题
        function handleHashRouting() {
            var hash = window.location.hash;
            console.log('当前hash:', hash);

            // 检查是否是标签页ID格式
            if (hash && hash.match(/^#\/tab_\d+$/)) {
                var tabId = hash.replace('#/', '');
                console.log('检测到标签页hash路由，尝试恢复标签页:', tabId);

                // 尝试从sessionStorage恢复标签页信息
                var tabInfoStr = sessionStorage.getItem('tabInfo_' + tabId);
                if (tabInfoStr) {
                    try {
                        var tabInfo = JSON.parse(tabInfoStr);
                        console.log('找到标签页信息:', tabInfo);

                        // 等待layui加载完成后恢复标签页
                        setTimeout(function() {
                            if (window.layui && window.layui.element) {
                                var element = window.layui.element;

                                // 重新创建标签页
                                element.tabAdd('layuiminiTab', {
                                    title: '<span class="layuimini-tab-active"></span><span>' + tabInfo.title + '</span><i class="layui-icon layui-unselect layui-tab-close">ဆ</i>',
                                    content: '<iframe src="' + tabInfo.url + '" frameborder="0" style="width:100%;height:100%;" data-url="' + tabInfo.url + '" data-id="' + tabInfo.id + '"></iframe>',
                                    id: tabInfo.id
                                });

                                element.tabChange('layuiminiTab', tabInfo.id);
                                sessionStorage.setItem('layuiminimenu_' + tabInfo.id, tabInfo.title);

                                console.log('标签页恢复成功:', tabInfo.title);
                                return;
                            }
                        }, 1000);
                    } catch (e) {
                        console.error('解析标签页信息失败:', e);
                    }
                } else {
                    console.warn('未找到标签页信息，无法恢复');
                }

                // 如果无法恢复，清除hash回到首页
                console.log('无法恢复标签页，清除hash');
                window.location.hash = '';
                return;
            }

            // 检查是否是具体页面的hash路由
            if (hash && hash.indexOf('#/AssignFlow/FlowApply') > -1) {
                console.log('检测到FlowApply页面hash路由');

                // 提取参数
                var params = {};
                if (hash.indexOf('?') > -1) {
                    var queryString = hash.split('?')[1];
                    var pairs = queryString.split('&');
                    for (var i = 0; i < pairs.length; i++) {
                        var pair = pairs[i].split('=');
                        if (pair.length === 2) {
                            params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);
                        }
                    }
                }

                // 如果有RFQNo参数，创建新标签页
                if (params.RFQNo) {
                    console.log('恢复FlowApply页面，RFQNo:', params.RFQNo);

                    // 等待layui加载完成后创建标签页
                    setTimeout(function() {
                        if (window.layui && window.layui.element) {
                            var element = window.layui.element;
                            var tabId = 'tab_' + Date.now();
                            var url = 'AssignFlow/FlowApply?RFQNo=' + params.RFQNo;
                            var title = '签核流程申请 - ' + params.RFQNo;

                            element.tabAdd('layuiminiTab', {
                                title: '<span class="layuimini-tab-active"></span><span>' + title + '</span><i class="layui-icon layui-unselect layui-tab-close">ဆ</i>',
                                content: '<iframe src="' + url + '" frameborder="0" style="width:100%;height:100%;" data-url="' + url + '" data-id="' + tabId + '"></iframe>',
                                id: tabId
                            });

                            element.tabChange('layuiminiTab', tabId);
                            sessionStorage.setItem('layuiminimenu_' + tabId, title);

                            // 清除hash
                            window.location.hash = '';
                        }
                    }, 1000);
                }
            }
        }

        // 监听标签页切换事件，同步更新hash
        function setupTabSwitchListener() {
            if (window.layui && window.layui.element) {
                // 监听标签页切换
                window.layui.element.on('tab(layuiminiTab)', function(data) {
                    var tabId = data.elem.getAttribute('lay-id');
                    if (tabId && tabId.startsWith('tab_')) {
                        console.log('标签页切换到:', tabId);
                        window.location.hash = '#/' + tabId;
                    } else if (!tabId || tabId === 'layuiminiHomeTabIframe') {
                        // 切换到首页
                        console.log('切换到首页');
                        window.location.hash = '';
                    }
                });

                // 监听标签页删除事件
                window.layui.element.on('tabDelete(layuiminiTab)', function(data) {
                    var tabId = data.elem.getAttribute('lay-id');
                    if (tabId && tabId.startsWith('tab_')) {
                        console.log('删除标签页:', tabId);
                        // 清理sessionStorage
                        sessionStorage.removeItem('tabInfo_' + tabId);
                        sessionStorage.removeItem('layuiminimenu_' + tabId);

                        // 如果删除的是当前标签页，清除hash
                        if (window.location.hash === '#/' + tabId) {
                            window.location.hash = '';
                        }
                    }
                });

                console.log('标签页事件监听器设置完成');
            } else {
                // 如果layui还没加载完成，延迟重试
                setTimeout(setupTabSwitchListener, 500);
            }
        }

        // 页面加载完成后处理hash路由和设置监听器
        $(document).ready(function() {
            handleHashRouting();
            setupTabSwitchListener();
        });
    });
</script>
</body>
</html>