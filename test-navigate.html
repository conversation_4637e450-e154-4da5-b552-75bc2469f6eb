<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>测试navigateInNewTab函数</title>
    <script src="js/navigate.js"></script>
</head>
<body>
    <h2>测试navigateInNewTab函数</h2>
    <p>这个页面用于测试修改后的navigateInNewTab函数是否正常工作。</p>
    
    <button onclick="testNavigate()">测试打开新标签页</button>
    <button onclick="debugIframeInfo()">调试iframe信息</button>
    
    <div id="log" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc; background: #f9f9f9;">
        <h3>日志输出：</h3>
        <div id="logContent"></div>
    </div>

    <script>
        // 重写console.log来显示在页面上
        var originalLog = console.log;
        console.log = function() {
            originalLog.apply(console, arguments);
            var logContent = document.getElementById('logContent');
            var message = Array.prototype.slice.call(arguments).join(' ');
            logContent.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
        };

        function testNavigate() {
            console.log('开始测试navigateInNewTab函数...');
            var result = navigateInNewTab("../AssignFlow/FlowApply?RFQNo=TEST001", "测试签核流程申请");
            console.log('函数返回结果:', result);
        }
    </script>
</body>
</html>
