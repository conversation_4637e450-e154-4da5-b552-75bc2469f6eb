﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="QueryQuote.aspx.cs" Inherits="WebApplication1.DataManager.QueryQuote" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8">
    <title>报价单查询</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../lib/layui-v2.9.21/css/layui.css" media="all">
    <link rel="stylesheet" href="../css/public.css" media="all">
    <style>
        body {
            background-color: #f2f2f2;
            margin: 0;
            padding: 0;
        }
        .layui-form {
            padding: 20px;
            background-color: #fff;
            margin: 20px auto;
            max-width: 1200px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-radius: 4px;
        }
        .table-container {
            padding: 0 20px;
            margin: 0 auto;
            max-width: 1200px;
        }
        .layui-table-body {
            height: calc(100vh - 250px);
            overflow-y: auto;
        }
        .download-btn {
            margin-right: 5px;
        }
        .file-button {
            display: inline-block;
            padding: 6px 12px;
            margin-right: 10px;
            background-color: #1E9FFF;
            color: #fff;
            border-radius: 3px;
            cursor: pointer;
            border: none;
            transition: all 0.3s ease;
        }
        .file-button:hover {
            background-color: #0d8ae6;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        .layui-table-cell {
            height: 40px;
            line-height: 40px;
            padding: 0 15px;
        }
        .rfq-link {
            color: #1E9FFF;
            cursor: pointer;
            font-weight: 500;
        }
        .rfq-link:hover {
            color: #0d8ae6;
            text-decoration: underline;
        }
        /* 新增弹窗样式 */
        .quote-detail-popup {
            padding: 0 !important;
        }
        .quote-detail-popup .layui-layer-content {
            padding: 0 !important;
        }
        .quote-detail-table {
            margin: 0 !important;
            padding: 0 !important;
        }
        .quote-detail-container {
            padding: 0 !important;
        }
        /* 美化表格样式 */
        .layui-table {
            width: 100% !important;
            margin: 0 !important;
        }
        .layui-table thead tr {
            background-color: #f8f8f8;
        }
        .layui-table thead th {
            font-weight: bold;
            color: #333;
            border-bottom: 2px solid #e6e6e6;
        }
        .layui-table tbody tr:hover {
            background-color: #f5f7fa !important;
        }
        .layui-table td {
            border-bottom: 1px solid #eee;
        }
        /* 状态样式优化 */
        .status-badge {
            padding: 4px 8px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-pending {
            background-color: #fff7e6;
            color: #FFB800;
        }
        .status-completed {
            background-color: #e6f7f2;
            color: #009688;
        }
        .status-unsent {
            background-color: #fff1f0;
            color: #FF5722;
        }

    </style>
    
    
    
</head>
<body>
    <!-- 查询表单 -->
    <form class="layui-form">
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">RFQ单号</label>
                <div class="layui-input-inline">
                    <input type="text" name="RFQNo" placeholder="请输入RFQ单号" autocomplete="off" class="layui-input">
                </div>
            </div>
            
            <div class="layui-inline">
                <button class="layui-btn" lay-submit lay-filter="searchForm">查询</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>

    <!-- 数据表格 -->
    <div class="table-container">
        <table id="rfqTable" lay-filter="rfqTable"></table>
    </div>
    <!-- 表格工具栏模板 -->
    <script type="text/html" id="fileTemplate">
         {{# if(d.file1){ }}
        <button class="file-button" lay-event="download1">{{getFileName(d.file1)}}</button>
    {{# } }}
    {{# if(d.file2){ }}
        <button class="file-button" lay-event="download2">{{getFileName(d.file2)}}</button>
    {{# } }}
    {{# if(d.file3){ }}
        <button class="file-button" lay-event="download3">{{getFileName(d.file3)}}</button>
    {{# } }}
    </script>
   <script src="../lib/layui-v2.9.21/layui.js" charset="utf-8"></script>
    <script src="../js/navigate.js" charset="utf-8"></script>
    

    <script>
        

        layui.use(['table', 'form', 'layer', 'jquery'], function () {
        var table = layui.table;
        var form = layui.form;
        var layer = layui.layer;
        var $ = layui.$;
            // 使用全局变量记录当前打开的RFQNo，防止重复打开
            var currentOpenRFQNo = null;
            
            // 获取文件名函数
            window.getFileName = function (filePath) {
                if (!filePath) return '';
                var parts = filePath.split('\\');
                return parts[parts.length - 1];
            }

            var tableIns = table.render({
                elem: '#rfqTable',
                height: 'full-200',
                data:[],
                cols: [[
                    {
                        field: 'RFQNo',
                        title: 'RFQ单号',
                        width: 250,
                        templet: function (d) {
                            return '<span class="rfq-link" style="color: #1E9FFF; cursor: pointer; font-weight: 500;">' + d.RFQNo + '</span>';
                        }
                    },
                    {
                        field: 'CreateTime',
                        title: '创建日期',
                        width: 120
                    },
                    {
                        field: 'Status',
                        title: '状态',
                        width: 100,
                        templet: function (d) {
                            var statusMap = {
                                '4': '<span style="color: #FFB800">报价中</span>',
                                '5': '<span style="color: #009688">报价完成</span>',
                                '6': '<span style="color: #009688">报价完成</span>',
                                '7': '<span style="color: #009688">报价完成</span>',
                                default: '<span style="color: #FF5722">未发送报价</span>'
                            };
                            return statusMap[d.Status] || statusMap.default;
                        }
                    }
                ]],
                limit: 10000,
                text: {
                    none: '暂无相关数据'
                },
                even: true,
                size: 'lg'
            });

            // 监听单元格点击事件
            table.on('tool(rfqTable)', function (obj) {
                var data = obj.data;
                if (obj.event === 'click') {
                    showQuoteDetails(data.RFQNo);
                }
            });

            // 自定义点击RFQNo列
            $(document).off('click', '.rfq-link').on('click', '.rfq-link', function () {
                var RFQNo = $(this).text();

                // 如果点击的是同一个RFQ，直接返回
                if (currentOpenRFQNo === RFQNo) {
                    return;
                }

                // 重置当前打开的RFQNo
                currentOpenRFQNo = RFQNo;

                // 显示报价详情弹窗
                showQuoteDetails(RFQNo);
            });


            // 显示报价详情弹窗
            function showQuoteDetails(RFQNo) {
                // 先关闭所有已存在的弹窗
                layer.closeAll('page');
                console.log(RFQNo);
                // 打开新的弹窗
                layer.open({
                    type: 1,
                    title: 'RFQ详细报价信息 - ' + RFQNo,
                    area: ['95%', '90%'],
                    maxmin: true,
                    skin: 'quote-detail-popup',
                    content: '<div class="quote-detail-container"><table id="quotationTable" lay-filter="quotationTable"></table></div>',
                    success: function (layero, index) {
                        // 显示加载中
                        layer.load();

                        // 延迟加载，确保DOM已经准备好
                        setTimeout(function () {
                            loadQuoteDetails(RFQNo);
                        }, 100);
                    },
                    end: function () {
                        // 重置当前打开的RFQNo
                        currentOpenRFQNo = null;

                        // 清理可能残留的事件绑定
                        $('.next-step-container').off('click');
                    }
                });
            }

            // 加载详细报价信息
            function loadQuoteDetails(RFQNo) {
                // 销毁可能存在的表格实例
                table.render({
                    elem: '#quotationTable',
                    data:[], // 清空数据
                    cols: [[]] // 清空列配置
                });

                // 重新渲染表格
                table.render({
                    elem: '#quotationTable',
                    height: 'calc(100% - 50px)',
                    url: '../ashx/QuoteControl.ashx?action=QueryAllQuote&RFQNo=' + RFQNo,
                    method: 'get', // 显式指定方法
                    contentType: 'application/json', // 指定内容类型
                    cols: [[
                        {
                            field: 'quotationNo',
                            title: '报价单号',
                            width: '15%',
                            fixed: 'left',
                            templet: function (d) {
                                return '<a href="../Supplier/QuoteDetail.aspx?quotationNo=' + d.quotationNo + '" target="_blank" class="quotation-link">' + d.quotationNo + '</a>';
                            }
                        },
                        { field: 'materialGroup', title: '物料组别', width: '10%' },
                        { field: 'createDate', title: '创建日期', width: '10%' },
                        { field: 'quotationDate', title: '报价日期', width: '10%' },
                        {
                            field: 'status',
                            title: '状态',
                            width: '8%',
                            templet: function (d) {
                                var statusMap = {
                                    '0': '<span class="status-badge status-unsent">未报价</span>',
                                    '1': '<span class="status-badge status-pending">已拒绝</span>',
                                    '2': '<span class="status-badge status-pending">报价中</span>',
                                    '3': '<span class="status-badge status-completed">已报价</span>',
                                    default: '<span class="status-badge">未知状态</span>'
                                };
                                return statusMap[d.status] || statusMap.default;
                            }
                        },
                        {
                            title: '文件',
                            width: '47%',
                            templet: '#fileTemplate',
                            style: 'padding-left: 15px;'
                        }
                    ]],
                    limit: 10000,
                    text: {
                        none: '暂无报价单'
                    },
                    even: true,
                    size: 'lg',
                    done: function (res) {
                        // 关闭加载中
                        layer.closeAll('loading');

                        // 移除可能存在的之前的下一步按钮
                        $('.next-step-container').remove();

                        // 检查是否所有报价单都已拒绝或已报价
                        var allDone = res.data.every(function (item) {
                            return item.status == '1' || item.status == '3';
                        });

                        if (allDone) {
                            // 在弹窗底部添加下一步按钮
                            var nextStepBtn = $('<div class="next-step-container" style="text-align:center;padding:10px;"><button class="layui-btn layui-btn-normal">下一步</button></div>');
                            $('.quote-detail-container').append(nextStepBtn);


                            // 使用委托事件，避免重复绑定
                            $('.quote-detail-container').off('click', '.next-step-container').on('click', '.next-step-container', function () {
                                layer.closeAll();
                                // 使用FriendlyUrls格式（无.aspx扩展名）
                                navigateInNewTab("../AssignFlow/FlowApply?RFQNo=" + RFQNo, "签核流程申请");
                            });
                        }
                    },
                    error: function (xhr, status, error) {
                        // 错误处理
                        layer.closeAll('loading');
                        layer.msg('加载数据失败：' + error);
                        console.error('加载数据错误:', error);
                    }
                });
            }

        // 监听查询表单提交
        form.on('submit(searchForm)', function(data){
            loadData(data.field);
            layer.closeAll('loading');
            return false;
        });

        // 监听表格工具条事件
        table.on('tool(quotationTable)', function (obj) {
            var data = obj.data;
            var event = obj.event;
            console.log("触发下载事件:", event, data);

            if (event.startsWith('download')) {
                var fileNum = event.charAt(event.length - 1);
                var filePath = data['file' + fileNum];
                if (filePath) {
                    downloadFile(filePath);
                }
            }
        });

        // 加载数据函数
        function loadData(params) {
            // 实际使用时替换为真实的接口调用
            var url = '../ashx/QuoteControl.ashx?action=QuerySelfRFQ';
            layer.load();
            $.ajax({
                url: url,
                type: 'GET',
                data:params,
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        // 修正这里的重载方式
                        tableIns.reload({
                            data: res.data,
                            done: function () {
                                // 确保链接样式和可点击性
                                $('.rfq-link').css({
                                    'color': '#1E9FFF',
                                    'cursor': 'pointer',
                                    'font-weight': '500'
                                });
                            }
                        });
                    } else {
                        layer.msg('加载数据失败：' + res.msg);
                    }
                },
                error: function () {
                    layer.closeAll('loading');
                    layer.msg('请求失败，请检查网络连接');
                }
            });
        }

        
            // 下载文件函数
            function downloadFile(filePath) {
                // 实际使用时替换为真实的下载逻辑
                window.open('../ashx/QuoteControl.ashx?action=DownLoadFile&&filePath=' + encodeURIComponent(filePath));
            }
        

        // 初始加载数据
        loadData({});
    });
    </script>
</body>
</html>
